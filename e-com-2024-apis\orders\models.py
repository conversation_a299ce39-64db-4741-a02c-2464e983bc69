from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from products.models import Product, ProductVariant
from uuid import uuid4
from django.db.models import Sum, F, Count, Case, When, IntegerField
from datetime import timedelta
from django.db.models.functions import TruncMonth
from django.utils import timezone

User = get_user_model()

class Cart(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def total_items(self):
        return sum(item.quantity for item in self.items.all())

    @property
    def subtotal(self):
        return sum(item.line_total for item in self.items.all())

    def clear(self):
        self.items.all().delete()

class CartItem(models.Model):
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, null=True, blank=True)
    quantity = models.PositiveIntegerField(default=1, validators=[MinValueValidator(1)])
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('cart', 'product', 'variant')

    @property
    def unit_price(self):
        base_price = self.product.price
        if self.variant:
            base_price += self.variant.price_adjustment
        return base_price

    @property
    def line_total(self):
        return self.unit_price * self.quantity

class ShippingMethod(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    estimated_days = models.PositiveIntegerField()
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

class Order(models.Model):
    ORDER_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('PAID', 'Paid'),
        ('SHIPPED', 'Shipped'),
        ('DELIVERED', 'Delivered'),
        ('CANCELLED', 'Cancelled'),
        ('REFUNDED', 'Refunded'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False, unique=True)

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=ORDER_STATUS_CHOICES, default='PENDING')
    shipping_address = models.ForeignKey('users.Address', on_delete=models.PROTECT, related_name='shipping_orders')
    billing_address = models.ForeignKey('users.Address', on_delete=models.PROTECT, related_name='billing_orders')
    shipping_method = models.ForeignKey(ShippingMethod, on_delete=models.PROTECT, null=True, blank=True)

    subtotal = models.DecimalField(max_digits=10, decimal_places=2)
    gst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Total GST amount")
    cgst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="CGST amount")
    sgst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="SGST amount")
    igst_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="IGST amount (for inter-state)")
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2)
    total = models.DecimalField(max_digits=10, decimal_places=2)

    stripe_payment_intent_id = models.CharField(max_length=100, blank=True)
    stripe_client_secret = models.CharField(max_length=100, blank=True)

    # PhonePe fields
    phonepe_transaction_id = models.CharField(max_length=100, blank=True)
    phonepe_payment_url = models.URLField(max_length=500, blank=True)

    tracking_number = models.CharField(max_length=100, blank=True)
    estimated_delivery_date = models.DateField(null=True, blank=True)

    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Rapidshyp Integration Fields (added for shipping enhancement)
    rapidshyp_enabled = models.BooleanField(default=False,
                                          help_text="Whether this order uses Rapidshyp shipping")
    pickup_pincode = models.CharField(max_length=6, blank=True,
                                    help_text="Pickup pincode for Rapidshyp")
    delivery_pincode = models.CharField(max_length=6, blank=True,
                                      help_text="Delivery pincode for Rapidshyp")
    package_weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True,
                                       help_text="Package weight in kg for Rapidshyp")
    package_length = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True,
                                       help_text="Package length in cm")
    package_breadth = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True,
                                        help_text="Package breadth in cm")
    package_height = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True,
                                       help_text="Package height in cm")
    rapidshyp_rate_data = models.JSONField(null=True, blank=True,
                                         help_text="Selected Rapidshyp rate data")
    selected_courier_code = models.CharField(max_length=20, blank=True,
                                           help_text="Selected courier code from Rapidshyp")
    rapidshyp_order_id = models.CharField(max_length=100, blank=True,
                                        help_text="Rapidshyp order ID")

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['status']),
            models.Index(fields=['user']),
        ]

    def __str__(self):
        return f"Order #{self.id} - {self.user.email}"

    def calculate_gst_breakdown(self, is_inter_state=False):
        """Calculate GST breakdown for all items in the order"""
        total_gst_breakdown = {
            'igst_amount': 0,
            'cgst_amount': 0,
            'sgst_amount': 0,
            'total_gst': 0
        }

        for item in self.items.all():
            if item.product:
                item_breakdown = item.product.calculate_gst_breakdown(
                    quantity=item.quantity,
                    is_inter_state=is_inter_state
                )
                total_gst_breakdown['igst_amount'] += item_breakdown['igst_amount']
                total_gst_breakdown['cgst_amount'] += item_breakdown['cgst_amount']
                total_gst_breakdown['sgst_amount'] += item_breakdown['sgst_amount']
                total_gst_breakdown['total_gst'] += item_breakdown['total_gst']

        # Round all amounts
        for key in total_gst_breakdown:
            total_gst_breakdown[key] = round(total_gst_breakdown[key], 2)

        return total_gst_breakdown

    def update_gst_amounts(self, is_inter_state=False):
        """Update GST amounts in the order based on current items"""
        gst_breakdown = self.calculate_gst_breakdown(is_inter_state)

        self.gst_amount = gst_breakdown['total_gst']
        self.cgst_amount = gst_breakdown['cgst_amount']
        self.sgst_amount = gst_breakdown['sgst_amount']
        self.igst_amount = gst_breakdown['igst_amount']

        # Update total to include GST
        self.total = self.subtotal + self.gst_amount + self.shipping_cost

        return gst_breakdown

    def is_inter_state_transaction(self) -> bool:
        """
        Determine if this order is an inter-state transaction based on billing and shipping addresses

        Returns:
            bool: True if inter-state, False if intra-state
        """
        if not self.billing_address or not self.shipping_address:
            return False

        # Compare states
        billing_state = getattr(self.billing_address, 'state', '').strip().lower()
        shipping_state = getattr(self.shipping_address, 'state', '').strip().lower()

        return billing_state != shipping_state

    def get_correct_gst_breakdown(self):
        """
        Get the correct GST breakdown for this order based on transaction type and product-specific rates

        Returns:
            Dict containing correct GST breakdown
        """
        from .gst_service import gst_service
        return gst_service.calculate_order_gst_breakdown(self)

    @property
    def subtotal_with_gst(self):
        """Get subtotal including GST"""
        return self.subtotal + self.gst_amount

    @property
    def has_rapidshyp_shipment(self):
        """Check if order has associated Rapidshyp shipment"""
        return hasattr(self, 'rapidshyp_shipment') and self.rapidshyp_shipment is not None

    @property
    def shipping_tracking_info(self):
        """Get shipping tracking information (Rapidshyp or standard)"""
        if self.has_rapidshyp_shipment:
            shipment = self.rapidshyp_shipment
            return {
                'type': 'rapidshyp',
                'tracking_number': shipment.awb_number,
                'courier_name': shipment.courier_name,
                'status': shipment.current_status,
                'tracking_url': shipment.tracking_url,
                'estimated_delivery': shipment.expected_delivery_date
            }
        else:
            return {
                'type': 'standard',
                'tracking_number': self.tracking_number,
                'courier_name': self.shipping_method.name if self.shipping_method else '',
                'status': self.status,
                'tracking_url': '',
                'estimated_delivery': self.estimated_delivery_date
            }
    # @staticmethod
    # def get_product_performance():
    #     last_month = timezone.now() - timedelta(days=30)

    #     products = OrderItem.objects.values('product_id', 'product__name').annotate(
    #         sales=Sum('quantity'),
    #         revenue=Sum(F('quantity') * F('unit_price')),
    #         last_month_sales=Sum(Case(
    #             When(order__created_at__gte=last_month, then=F('quantity')),
    #             default=0,
    #             output_field=IntegerField()
    #         ))
    #     )

    #     data = []
    #     for product in products:
    #         growth = (
    #             ((product['sales'] - product['last_month_sales']) / product['last_month_sales']) * 100
    #             if product['last_month_sales'] else 0
    #         )
    #         data.append({
    #             'id': product['product_id'],
    #             'name': product['product__name'],
    #             'sales': product['sales'],
    #             'revenue': product['revenue'],
    #             'growth': f"{growth:.1f}%"
    #         })
    #     return data

    # @staticmethod
    # def get_sales_chart():
    #     data = (
    #         Order.objects
    #         .annotate(month=TruncMonth('created_at'))
    #         .values('month')
    #         .annotate(sales=Sum(F('items__quantity') * F('items__unit_price')))
    #         .order_by('month')
    #     )
    #     return [{'name': month['month'].strftime('%b'), 'sales': month['sales']} for month in data]


class OrderItem(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.SET_NULL, null=True)
    variant = models.ForeignKey(ProductVariant, on_delete=models.SET_NULL, null=True, blank=True)
    quantity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)

    # Store product details at the time of purchase
    product_name = models.CharField(max_length=255)
    variant_name = models.CharField(max_length=100, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['product']),
        ]

    def __str__(self):
        return f"{self.product_name} - {self.quantity} units"

class Payment(models.Model):
    PAYMENT_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('REFUNDED', 'Refunded'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('CARD', 'Credit/Debit Card'),
        ('STRIPE', 'Stripe'),
        ('PHONEPE', 'PhonePe'),
        ('COD', 'Cash on Delivery'),
    ]

    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='PENDING')
    payment_method = models.CharField(max_length=50, choices=PAYMENT_METHOD_CHOICES, default='CARD')
    transaction_id = models.CharField(max_length=100, blank=True)
    phonepe_transaction_details = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Payment {self.transaction_id} for Order #{self.order.id}"


class Invoice(models.Model):
    """Invoice model for generating GST-compliant invoices"""
    order = models.OneToOneField(Order, on_delete=models.CASCADE, related_name='invoice')
    invoice_number = models.CharField(max_length=50, unique=True, help_text="Format: INV-YYYY-MM-XXXXXX")
    generated_at = models.DateTimeField(auto_now_add=True)
    pdf_file = models.FileField(upload_to='invoices/', blank=True, null=True)

    # Company details (can be moved to settings later)
    company_name = models.CharField(max_length=200, default="Triumph Enterprises")
    company_address = models.TextField(default="D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri, Patel Nagar, Darussalam, Hyderabad, Telangana 500001, India")
    company_email = models.EmailField(default="<EMAIL>")
    company_phone = models.CharField(max_length=20, default="+91 9848486452")
    gst_number = models.CharField(max_length=20, blank=True, help_text="Company GST registration number")

    class Meta:
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['invoice_number']),
            models.Index(fields=['generated_at']),
        ]

    def __str__(self):
        return f"Invoice {self.invoice_number} for Order #{self.order.id}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate unique invoice number in format INV-YYYY-MM-XXXXXX"""
        from datetime import datetime
        now = datetime.now()
        year_month = now.strftime('%Y-%m')

        # Get the last invoice number for this month
        last_invoice = Invoice.objects.filter(
            invoice_number__startswith=f'INV-{year_month}'
        ).order_by('-invoice_number').first()

        if last_invoice:
            # Extract the sequence number and increment
            last_seq = int(last_invoice.invoice_number.split('-')[-1])
            new_seq = last_seq + 1
        else:
            new_seq = 1

        return f'INV-{year_month}-{new_seq:06d}'