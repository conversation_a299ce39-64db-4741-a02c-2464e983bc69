/**
 * Rapidshyp Rate Calculator Component - Main shipping rate calculator
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calculator, Clock, MapPin, Package, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PincodeValidator } from './PincodeValidator';
import { ShippingRateSelector } from './ShippingRateSelector';
import { useShippingRates } from '@/hooks/useShippingRates';
import type { RapidshypRateCalculatorProps, CourierRate } from '@/types/shipping';

export const RapidshypRateCalculator: React.FC<RapidshypRateCalculatorProps> = ({
  deliveryPincode,
  orderWeight = 1.0,
  isCOD = false,
  orderTotal = 0,
  onRateSelect,
  selectedRate,
  className,
  autoSelectLowest = false,
  hideRateSelection = false
}) => {
  const [pincode, setPincode] = useState(deliveryPincode);
  const [isPincodeValid, setIsPincodeValid] = useState(false);
  const [isPincodeServiceable, setIsPincodeServiceable] = useState(false);
  const [hasCalculatedRates, setHasCalculatedRates] = useState(false);

  const {
    rates,
    loading,
    error,
    source,
    minimumRate,
    calculateRates,
    clearRates
  } = useShippingRates();

  // Update pincode when prop changes
  useEffect(() => {
    setPincode(deliveryPincode);
    // Auto-validate pincode if autoSelectLowest is enabled
    if (autoSelectLowest && deliveryPincode && deliveryPincode.length === 6) {
      setIsPincodeValid(true);
      setIsPincodeServiceable(true);
    }
  }, [deliveryPincode, autoSelectLowest]);

  // Calculate rates when pincode becomes valid and serviceable
  const handleRateCalculation = useCallback(async () => {
    if (isPincodeValid && isPincodeServiceable && pincode.length === 6) {
      try {
        await calculateRates({
          delivery_pincode: pincode,
          weight: orderWeight,
          cod: isCOD,
          total_value: orderTotal
        });
        setHasCalculatedRates(true);
      } catch (err) {
        console.error('Rate calculation failed:', err);
      }
    }
  }, [isPincodeValid, isPincodeServiceable, pincode, orderWeight, isCOD, orderTotal, calculateRates]);

  // Auto-calculate rates when conditions are met
  useEffect(() => {
    if (isPincodeValid && isPincodeServiceable && !hasCalculatedRates) {
      const timeoutId = setTimeout(handleRateCalculation, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [isPincodeValid, isPincodeServiceable, hasCalculatedRates, handleRateCalculation]);

  // Clear rates when pincode becomes invalid
  useEffect(() => {
    if (!isPincodeValid || !isPincodeServiceable) {
      clearRates();
      setHasCalculatedRates(false);
    }
  }, [isPincodeValid, isPincodeServiceable, clearRates]);

  // Auto-select minimum rate if no rate is selected (or if autoSelectLowest is enabled)
  useEffect(() => {
    if (rates.length > 0 && !selectedRate && minimumRate && autoSelectLowest) {
      onRateSelect(minimumRate);
    }
  }, [rates.length, selectedRate, minimumRate?.courier_code, autoSelectLowest]);

  const handlePincodeChange = (newPincode: string) => {
    setPincode(newPincode);
    setHasCalculatedRates(false);
  };

  const handleValidationChange = (isValid: boolean, isServiceable: boolean) => {
    setIsPincodeValid(isValid);
    setIsPincodeServiceable(isServiceable);
  };

  const handleRateSelection = (rate: CourierRate) => {
    onRateSelect(rate);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const shouldShowRates = isPincodeValid && isPincodeServiceable && (rates.length > 0 || loading || error);

  // If hideRateSelection is true, show loading or selected rate
  if (hideRateSelection) {
    if (loading) {
      return (
        <div className="space-y-4">
          <div className="p-6 border-2 border-blue-200 rounded-lg bg-blue-50">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
              <div className="text-blue-700 font-medium">Finding best shipping rate...</div>
            </div>
          </div>
        </div>
      );
    }

    if (selectedRate) {
      return (
        <div className="space-y-4">
          <div className="p-6 border-2 border-green-200 rounded-lg bg-green-50">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <div className="font-semibold text-xl text-green-800">{selectedRate.courier_name}</div>
                  <Badge variant="default" className="bg-green-100 text-green-700 border-green-200">
                    Best Rate
                  </Badge>
                </div>
                <div className="text-sm text-green-700 flex items-center gap-1 mb-1">
                  <Clock className="h-4 w-4" />
                  {selectedRate.estimated_delivery_date ? (
                    (() => {
                      try {
                        const [day, month, year] = selectedRate.estimated_delivery_date.split('-');
                        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                        const formattedDate = date.toLocaleDateString('en-IN', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric'
                        });
                        return `Delivery by ${formattedDate}`;
                      } catch {
                        return selectedRate.estimated_days ?
                          `${selectedRate.estimated_days} business days` :
                          'Standard delivery';
                      }
                    })()
                  ) : (
                    selectedRate.estimated_days ?
                      `${selectedRate.estimated_days} business days` :
                      'Standard delivery'
                  )}
                  {selectedRate.parent_courier_name && ` via ${selectedRate.parent_courier_name}`}
                </div>
                <div className="text-xs text-green-600">
                  {selectedRate.freight_mode} • Live rate from Rapidshyp
                </div>
              </div>
              <div className="text-right ml-4">
                <div className="font-bold text-2xl text-green-700">
                  {formatCurrency(selectedRate.total_freight)}
                </div>
                <div className="text-xs text-green-600">Shipping cost</div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // If no rate selected yet and autoSelectLowest is enabled, show loading
    if (autoSelectLowest) {
      return (
        <div className="space-y-4">
          <div className="p-6 border-2 border-gray-200 rounded-lg bg-gray-50">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600 mr-3"></div>
              <div className="text-gray-700 font-medium">Loading shipping options...</div>
            </div>
          </div>
        </div>
      );
    }
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center">
            <Calculator className="h-5 w-5 mr-2 text-primary" />
            Shipping Calculator
          </CardTitle>

          {source === 'rapidshyp' && (
            <Badge variant="default" className="bg-green-100 text-green-700 border-green-200">
              <Zap className="h-3 w-3 mr-1" />
              Live Rates
            </Badge>
          )}
        </div>

        <p className="text-sm text-gray-600">
          Get real-time shipping rates for your delivery location
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Pincode Input Section */}
        <div className="space-y-4">
          <div className="flex items-center text-sm font-medium text-gray-700">
            <MapPin className="h-4 w-4 mr-2" />
            Delivery Location
          </div>
          
          <PincodeValidator
            value={pincode}
            onChange={handlePincodeChange}
            onValidationChange={handleValidationChange}
            label="Enter delivery pincode"
            placeholder="6-digit pincode"
          />
        </div>

        {/* Order Details */}
        {(orderWeight > 0 || orderTotal > 0) && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center text-sm font-medium text-gray-700">
                <Package className="h-4 w-4 mr-2" />
                Package Details
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                {orderWeight > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Weight:</span>
                    <span className="font-medium">{orderWeight} kg</span>
                  </div>
                )}
                
                {orderTotal > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Order Value:</span>
                    <span className="font-medium">{formatCurrency(orderTotal)}</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment:</span>
                  <span className="font-medium">{isCOD ? 'Cash on Delivery' : 'Prepaid'}</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Shipping Rates Section */}
        {shouldShowRates && !hideRateSelection && (
          <>
            <Separator />
            <ShippingRateSelector
              rates={rates}
              selectedRate={selectedRate}
              onRateSelect={handleRateSelection}
              loading={loading}
              error={error}
              source={source || undefined}
            />
          </>
        )}

        {/* Show selected rate when hiding rate selection */}
        {hideRateSelection && selectedRate && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center text-sm font-medium text-gray-700">
                <Zap className="h-4 w-4 mr-2 text-green-600" />
                Selected Shipping Option
              </div>

              <div className="p-4 border-2 border-green-200 rounded-lg bg-green-50">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-green-800">{selectedRate.courier_name}</div>
                    <div className="text-sm text-green-600">
                      {selectedRate.estimated_delivery_date ? (
                        (() => {
                          try {
                            const [day, month, year] = selectedRate.estimated_delivery_date.split('-');
                            const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                            const formattedDate = date.toLocaleDateString('en-IN', {
                              weekday: 'short',
                              month: 'short',
                              day: 'numeric'
                            });
                            return `Delivery by ${formattedDate}`;
                          } catch {
                            return selectedRate.estimated_days ?
                              `${selectedRate.estimated_days} business days` :
                              'Standard delivery';
                          }
                        })()
                      ) : (
                        selectedRate.estimated_days ?
                          `${selectedRate.estimated_days} business days` :
                          'Standard delivery'
                      )}
                      {selectedRate.parent_courier_name && ` via ${selectedRate.parent_courier_name}`}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-lg text-green-700">
                      {formatCurrency(selectedRate.total_freight)}
                    </div>
                    <div className="text-xs text-green-600">Best rate</div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Minimum Rate Highlight */}
        {minimumRate && rates.length > 1 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-blue-700 font-medium">
                💡 Best Rate Available:
              </span>
              <span className="text-blue-900 font-semibold">
                {formatCurrency(minimumRate.total_freight)} via {minimumRate.courier_name}
              </span>
            </div>
          </div>
        )}

        {/* Help Text */}
        {!isPincodeValid && pincode.length === 0 && (
          <div className="text-center py-4">
            <p className="text-sm text-gray-500">
              Enter your delivery pincode to see available shipping options
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RapidshypRateCalculator;
